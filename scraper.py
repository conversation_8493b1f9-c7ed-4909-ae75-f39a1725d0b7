#!/usr/bin/env python3
"""
Rapidgator Folder Link Scraper
Scrapes links from paginated Rapidgator folder across multiple pages using multithreading.
"""

import requests
from bs4 import BeautifulSoup
import threading
import random
import time
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RapidgatorScraper:
    def __init__(self, base_url, start_page=1, end_page=5787, max_workers=25):
        self.base_url = base_url
        self.start_page = start_page
        self.end_page = end_page
        self.max_workers = max_workers
        self.extracted_links = set()  # Use set to avoid duplicates
        self.saved_links = set()  # Track links already saved to file
        self.lock = threading.Lock()
        self.file_lock = threading.Lock()  # Separate lock for file operations
        self.session = requests.Session()
        self.total_links_extracted = 0
        self.links_since_last_save = 0
        self.save_interval = 500  # Save every 500 links
        self.rest_interval = 10000  # Rest after every 10,000 links
        self.rest_duration = 600  # 10 minutes in seconds
        self.output_filename = 'extracted_links.txt'

        # User agents for rotation
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36'
        ]
    
    def get_random_headers(self):
        """Generate random headers with user agent rotation."""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def scrape_page(self, page_number):
        """Scrape a single page and extract Rapidgator file links."""
        url = f"{self.base_url}?page={page_number}"

        try:
            # Add random delay to avoid overwhelming the server
            time.sleep(random.uniform(0.1, 0.5))

            headers = self.get_random_headers()
            response = self.session.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Find all links that start with "https://rapidgator.net/file/"
            links = []
            for link in soup.find_all('a', href=True):
                href = link['href']
                if href.startswith('https://rapidgator.net/file/'):
                    links.append(href)
                elif href.startswith('/file/'):
                    # Handle relative URLs
                    full_url = urljoin('https://rapidgator.net', href)
                    links.append(full_url)

            # Thread-safe addition to the main collection and trigger saves/rests
            new_links_count = 0
            with self.lock:
                old_size = len(self.extracted_links)
                self.extracted_links.update(links)
                new_links_count = len(self.extracted_links) - old_size
                self.total_links_extracted += new_links_count
                self.links_since_last_save += new_links_count

                # Check if we need to save incrementally
                if self.links_since_last_save >= self.save_interval:
                    self.save_links_incrementally()

                # Check if we need to rest
                if self.total_links_extracted > 0 and self.total_links_extracted % self.rest_interval == 0:
                    self.take_rest_break()

            logger.info(f"Page {page_number}: Found {len(links)} links ({new_links_count} new)")
            return len(links)

        except requests.exceptions.RequestException as e:
            logger.error(f"Error scraping page {page_number}: {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error on page {page_number}: {e}")
            return 0
    
    def save_links_incrementally(self):
        """Save new links incrementally to avoid data loss."""
        try:
            with self.file_lock:
                # Find links that haven't been saved yet
                new_links = self.extracted_links - self.saved_links

                if new_links:
                    with open(self.output_filename, 'a', encoding='utf-8') as f:
                        for link in sorted(new_links):
                            f.write(f"{link}\n")

                    # Update saved links tracking
                    self.saved_links.update(new_links)
                    self.links_since_last_save = 0

                    logger.info(f"Incrementally saved {len(new_links)} new links. Total saved: {len(self.saved_links)}")

        except Exception as e:
            logger.error(f"Error saving links incrementally: {e}")

    def take_rest_break(self):
        """Take a 10-minute rest break after processing many links."""
        logger.info(f"Reached {self.total_links_extracted} total links. Taking a 10-minute rest break...")
        logger.info(f"Rest break started at {time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Will resume at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time() + self.rest_duration))}")

        # Save any remaining unsaved links before resting
        self.save_links_incrementally()

        # Sleep for the rest duration
        time.sleep(self.rest_duration)

        logger.info(f"Rest break completed. Resuming scraping operations...")

    def save_links_to_file(self, filename=None):
        """Save all extracted links to a file (final save)."""
        if filename is None:
            filename = self.output_filename

        try:
            # Save any remaining unsaved links
            self.save_links_incrementally()
            logger.info(f"Final save completed. Total unique links saved: {len(self.saved_links)}")
        except Exception as e:
            logger.error(f"Error in final save: {e}")
    
    def run(self):
        """Main method to run the scraper with multithreading."""
        logger.info(f"Starting scraper for pages {self.start_page} to {self.end_page}")
        logger.info(f"Using {self.max_workers} concurrent workers")
        logger.info(f"Incremental saving every {self.save_interval} links")
        logger.info(f"Rest breaks every {self.rest_interval} links for {self.rest_duration//60} minutes")

        # Initialize output file (clear it if it exists)
        try:
            with open(self.output_filename, 'w', encoding='utf-8') as f:
                pass  # Just create/clear the file
            logger.info(f"Initialized output file: {self.output_filename}")
        except Exception as e:
            logger.error(f"Error initializing output file: {e}")
            return

        total_pages = self.end_page - self.start_page + 1
        completed_pages = 0
        total_links_found = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all page scraping tasks
            future_to_page = {
                executor.submit(self.scrape_page, page): page
                for page in range(self.start_page, self.end_page + 1)
            }

            # Process completed tasks
            for future in as_completed(future_to_page):
                page = future_to_page[future]
                try:
                    links_found = future.result()
                    total_links_found += links_found
                    completed_pages += 1

                    if completed_pages % 100 == 0:
                        progress = (completed_pages / total_pages) * 100
                        with self.lock:
                            current_total = self.total_links_extracted
                            current_saved = len(self.saved_links)
                        logger.info(f"Progress: {completed_pages}/{total_pages} pages ({progress:.1f}%) | "
                                  f"Total links: {current_total} | Saved: {current_saved}")

                except Exception as e:
                    logger.error(f"Page {page} generated an exception: {e}")

        logger.info(f"Scraping completed!")
        logger.info(f"Total pages processed: {completed_pages}/{total_pages}")
        logger.info(f"Total unique links found: {len(self.extracted_links)}")

        # Final save of any remaining links
        self.save_links_to_file()


def main():
    """Main function to run the scraper."""
    base_url = "https://rapidgator.net/folder/3330879/movie.html"
    
    scraper = RapidgatorScraper(
        base_url=base_url,
        start_page=1,
        end_page=5787,
        max_workers=25
    )
    
    scraper.run()


if __name__ == "__main__":
    main()
